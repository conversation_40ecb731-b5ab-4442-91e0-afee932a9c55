import React, { useCallback, useEffect, useRef, useState } from 'react';
import TextUploadEditor from './TextUploadEditor';
import TextUploadMarkdown from './TextUploadMarkdown';
import type { IFileResponse } from '@/shared/types/global';
import type { ConversationDataType } from '@/features/project-management/types/questionnaire';
import { useChatBoxSetOnSubmitCallback } from '@/features/project-management/stores/chatbox-store';
import { useCopilotChat, useCopilotMessagesContext } from '@copilotkit/react-core';
import { useSaveMessageAI } from '@/features/project-management/hooks/useSaveMessageAI';
import { useMessageGetList } from '@/features/project-management/hooks/useMessageGetList';
import { ConvertMessageFromConversation, handleSaveMessage } from '@/features/project-management/utils/chat';
import type { MessageType } from '@/features/project-management/types/chat';
import type { TextMessage } from '@copilotkit/runtime-client-gql';
import { But<PERSON> } from '@/shared/components/ui/button';
import { useTranslations } from 'next-intl';
import { CheckBadgeIcon, FileEditIcon } from '@/shared/icons';

type TextUpLoadType = {
  data: any[];
  templates: IFileResponse[];
  stepId: string;
  id: string;
  evaluationFramework: string;
  nameForm: string;
  conversationData: ConversationDataType[];
  onBackDashboard: () => void;
  onBackUploadFile: () => void;
  getMarkdown: (markdown: string) => void;
  onOpenDetailScore: (data: string) => void;
};

export type TextUploadWrapperType = {
  confirmChangeRef?: () => void;
  discardChangeRef?: () => void;
  onSubmitRef?: () => void;
  toggleEditModeRef?: () => void;
};

type ResearchTypeView = 'research' | 'summarized';

const TextUploadWrapper: React.FC<TextUpLoadType> = ({
  data,
  templates,
  id,
  stepId,
  evaluationFramework,
  nameForm,
  conversationData,
  onBackDashboard,
  onBackUploadFile,
  getMarkdown,
  onOpenDetailScore,
}) => {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [markdownSummarized, setMarkdownSummarized] = useState<string>('');

  const [typeView, setTypeView] = useState<ResearchTypeView>('research');

  const { reset } = useCopilotChat();

  const { mutateAsync: saveMessageAI } = useSaveMessageAI();

  const lastSavedId = useRef<string | null>(null);

  const isInitialMessageRef = useRef<boolean>(true);

  const { messages, setMessages } = useCopilotMessagesContext();

  const [idConversation, setIdConversation] = useState<string>('');

  const { data: messageList } = useMessageGetList(idConversation);

  const setOnSubmitCallback = useChatBoxSetOnSubmitCallback();

  const conversationIdSummarized = conversationData.find(c => c.order === 1)?.id ?? '';

  const conversationIdResearch = conversationData.find(c => c.order === 0)?.id ?? '';

  const textUploadRef = useRef<TextUploadWrapperType>(null);

  const t = useTranslations('workflow');

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setIdConversation(typeView === 'summarized' ? conversationIdSummarized : conversationIdResearch);
  }, [conversationIdResearch, conversationIdSummarized, typeView]);

  // Function to handle when a message is submitted in ChatBox
  const handleChatBoxSubmit = useCallback(() => {
    isInitialMessageRef.current = false;
  }, []);

  // Register the callback with the chatBox store
  useEffect(() => {
    setOnSubmitCallback(handleChatBoxSubmit);

    // Cleanup: remove the callback when component unmounts
    return () => {
      setOnSubmitCallback(null);
    };
  }, [setOnSubmitCallback, handleChatBoxSubmit]);

  const saveMessage = useCallback(async (conversationId: string, data: TextMessage) => {
    await saveMessageAI({
      conversationId,
      data,
    });
  }, []);

  useEffect(() => {
    const conversationId = (typeView === 'summarized' ? conversationIdSummarized : conversationIdResearch);

    const CheckConversationIdExist = () => {
      if (!conversationId) {
        // eslint-disable-next-line no-useless-return
        return;
      }
    };

    const latest = handleSaveMessage(
      messages,
      lastSavedId,
      isInitialMessageRef,
      conversationId,
      saveMessage,
      CheckConversationIdExist,
    );
    latest.then(
      res => lastSavedId.current = res?.id ?? '',
    );
  }, [messages, typeView, conversationIdSummarized, conversationIdResearch, saveMessage]);

  useEffect(() => {
    reset();
    if (messageList && messageList.items.length) {
      const messages = [...messageList.items];
      const dataMessage = (messages.reverse().flatMap(d => d.data[0]) ?? []) as MessageType[];

      setMessages(ConvertMessageFromConversation(dataMessage));
    }

    return () => {
      reset();
    };
  }, [messageList, setMessages, reset]);

  useEffect(() => {
    getMarkdown(markdown);
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [markdown]);

  const handleChange = (data: string) => {
    console.log(data);
    if (typeView === 'research') {
      setMarkdown(data);
    } else {
      setMarkdownSummarized(data);
    }
  };

  const confirmChange = () => {
    if (textUploadRef.current?.confirmChangeRef) {
      textUploadRef.current.confirmChangeRef();
    }
  };

  const discardChange = () => {
    if (textUploadRef.current?.discardChangeRef) {
      textUploadRef.current.discardChangeRef();
    }
  };

  const onSubmit = () => {
    if (textUploadRef.current?.onSubmitRef) {
      textUploadRef.current?.onSubmitRef();
    }
  };

  const toggleEditMode = () => {
    setIsEditMode(true);
  };

  return (
    <React.Fragment>
      { isEditMode
        ? (
            <TextUploadEditor
              markdown={markdown}
              stepId={stepId}
              data={data}
              markdownSummarized={markdownSummarized}
              typeView={typeView}
              ref={textUploadRef}
              setMarkdownSummarized={setMarkdownSummarized}
              onChangeEditMode={setIsEditMode}
              onDataChange={handleChange}
            />
          )
        : (
            <TextUploadMarkdown
              markdown={markdown}
              markdownSummarized={markdownSummarized}
              data={data}
              templates={templates}
              id={id}
              stepId={stepId}
              nameForm={nameForm}
              typeView={typeView}
              ref={textUploadRef}
              evaluationFramework={evaluationFramework}
              conversationIdSummarized={conversationIdSummarized}
              conversationIdResearch={conversationIdResearch}
              setMarkdown={setMarkdown}
              onBackDashboard={onBackDashboard}
              onBackUploadFile={onBackUploadFile}
              setIsEditResearch={setIsEditMode}
              onOpenDetailScore={onOpenDetailScore}
              setTypeView={setTypeView}
              setMarkdownSummarized={setMarkdownSummarized}
            />
          )}

      <div className="sticky bottom-1 flex w-full items-center justify-center z-100">
        {isEditMode
          ? (
              <>
                <Button
                  type="button"
                  variant="outline"
                  onClick={discardChange}
                >
                  {t('common.discard')}
                </Button>

                <Button
                  type="button"
                  onClick={confirmChange}
                >
                  <CheckBadgeIcon className="h-5 w-5 " />
                  {t('common.confirm')}
                </Button>
              </>
            )
          : (
              <div className="flex items-center gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={toggleEditMode}
                >
                  <FileEditIcon className="h-5 w-5 " />
                  {t('common.edit')}
                </Button>

                <Button
                  type="button"
                  onClick={() => onSubmit()}
                >
                  <CheckBadgeIcon className="h-5 w-5 " />
                  {t('common.approve')}
                </Button>
              </div>
            )}

      </div>
    </React.Fragment>
  );
};

export default TextUploadWrapper;
