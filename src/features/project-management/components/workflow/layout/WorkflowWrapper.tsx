'use client';

import React, { useEffect, useRef } from 'react';
import { useProjectDetail, useProjectSteps } from '@/features/project-management/hooks';
import WorkflowSkeleton from './WorkflowSkeleton';
import {
  useCurrentStep,
  useCurrentTask,
  useProjectDetailActions,
  useWorkflowStore,
  // useInitializeWorkflow,

} from '@/features/project-management/stores/project-workflow-store';
import { cn } from '@/shared/utils/utils';
import { ChatBox } from '../chat-with-ai/ChatB<PERSON>';
import TaskList from './TaskList';
import TaskSidebar from './TaskSidebar';
import { useChatBoxHide, useChatBoxVisible, useToggleShowChatBox } from '@/features/project-management/stores/chatbox-store';
import EvaluationForm from '../evaluation-form/EvaluationForm';
import InitialScreeningForm from '../initial-screening-form/InitialScreeningForm';
import ScreeningOutcomeWrapper from '../screening-outcome/ScreeningOutcomeWrapper';
import { ETypeStep } from '@/features/project-management/types/workflow';
import type { WorkflowStep } from '@/features/project-management/types/workflow';
import ClientUploadForm from '../client-upload-form/ClientUploadForm';
import BriefAnalysisWrapper from '../brief-analysis/BriefAnalysisWrapper';
import GenerationFormWrapper from '../generation-form/GenerationFormWrapper';
import { useCoAgent } from '@copilotkit/react-core';
import type { stateRouteAgent } from '@/shared/types/global';
import { AGENT_NAME_COPILOTKIT, AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { useParams } from 'next/navigation';
import BriefStandardizedWrapper from '../brief-analysis/BriefStandardizedWrapper';
import ReviewInputWrapper from '../review-input/ReviewInputWrapper';
import GeneratedQuestionnaire from '../questionnaire/GeneratedQuestionnaire';
import ReportWrapper from '../discovery-questionnaire/ReportWrapper';
import { BussinessResearchList } from '../bussiness-research-form';
import { DocumentResearchList } from '../document-research-form';
import { getCurrentStepInLocalStorage, removeCurrentStepInLocalStorage } from '@/features/project-management/utils/workflowUtils';
import { useLocale } from 'next-intl';

export type WorkflowTypeRef = {
  onCheckSaveData: () => void;
};

const WorkflowWrapper: React.FC<{ projectId: string }> = ({ projectId }) => {
  const { data: projectSteps, isLoading } = useProjectSteps(projectId);

  const locale = useLocale();

  const currentTask = useCurrentTask();
  const currentStep = useCurrentStep();

  const toggleShowChatBox = useToggleShowChatBox();

  const hide = useChatBoxHide();

  const workflowStore = useWorkflowStore()!.getState();
  const { actions } = workflowStore;
  const { initializeWorkflow, moveToCurrentStep, setProjectName } = actions;

  const params = useParams<{ id: string }>();

  const { data: project } = useProjectDetail(params.id);

  const { saveProjectInfo } = useProjectDetailActions();

  const { state: _agentState, setState } = useCoAgent<stateRouteAgent<any>>({
    name: AGENT_ROUTE_NAME,
  });

  const prevId = useRef<string>('');

  useEffect(() => {
    if (prevId.current && prevId.current !== params.id) {
      removeCurrentStepInLocalStorage();
    }
    prevId.current = params.id;
  }, [params.id]);

  useEffect(() => {
    setProjectName(project?.name ?? '');
    if (project) {
      saveProjectInfo(project);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [project]);

  useEffect(() => {
    setState(prev => ({
      ...prev,
      project_id: params.id,
      agent_name: prev?.agent_name || AGENT_NAME_COPILOTKIT.SUMMARIZE, // Ensure agent_name is set
    }));
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params.id]);

  useEffect(() => {
    return () => {
      removeCurrentStepInLocalStorage();
    };
  }, []);

  // const { initializeWorkflow } = useWorkflowActions();

  // Initialize workflow when projectSteps data is available
  useEffect(() => {
    if (isLoading) {
      return;
    }
    // const workflowTasks = transformProjectStepsToHierarchy(projectSteps ?? []);
    const workflowTasks: WorkflowStep[] = (projectSteps ?? []).map(p => ({ ...p, steps: p.children ?? [] }));
    initializeWorkflow(workflowTasks);
    const { currentTask, currentStep } = getCurrentStepInLocalStorage();
    if (currentStep && currentTask) {
      moveToCurrentStep(currentTask, currentStep);
    }
  }, [projectSteps, initializeWorkflow, moveToCurrentStep, isLoading]); // initializeWorkflow is stable in Zustand but included for ESLint

  const isVisible = useChatBoxVisible();

  useEffect(() => {
    const typeNotShowChatBox = [ETypeStep.FORM, ETypeStep.OUTCOME, ETypeStep.UPLOAD, ETypeStep.REVIEW_1, ETypeStep.REVIEW_2];
    if (typeNotShowChatBox.find(type => type === currentStep?.type)) {
      toggleShowChatBox(false);
      hide();
    } else {
      toggleShowChatBox(true);
    }
  }, [currentStep, toggleShowChatBox, hide]);

  // Render the appropriate step component based on the current step
  const renderStepComponent = () => {
    if (!currentStep) {
      return null;
    }

    // Render based on step ID and type
    if (currentStep.type === ETypeStep.FORM) {
      return <InitialScreeningForm />;
    } else if (currentStep.type === ETypeStep.EVALUATION) {
      return <EvaluationForm />;
    } else if (currentStep.type === ETypeStep.OUTCOME) {
      return <ScreeningOutcomeWrapper />;
    } else if (currentStep.type === ETypeStep.UPLOAD) {
      return <ClientUploadForm />;
    } else if (currentStep.type === ETypeStep.GENERATE) {
      return <BriefAnalysisWrapper />;
    } else if (currentStep.type === ETypeStep.STANDARDIZED) {
      return <BriefStandardizedWrapper />;
    } else if (currentStep.type === ETypeStep.REVIEW_1 || currentStep.type === ETypeStep.REVIEW_2) {
      return <ReviewInputWrapper type={currentStep.type} />;
    } else if (currentStep.type === ETypeStep.GENERATED || currentStep.type === ETypeStep.GENERATED2) {
      return <GenerationFormWrapper type={currentStep.type} />;
    } else if (currentStep.type === ETypeStep.QUESTIONNAIRE) {
      return <GeneratedQuestionnaire />;
    } else if (currentStep.type === ETypeStep.REPORT) {
      return <ReportWrapper />;
    } else if (currentStep.type === ETypeStep.RESEARCH) {
      return <BussinessResearchList />;
    } else if (currentStep.type === ETypeStep.DOCUMENT_2) {
      return <DocumentResearchList />;
    } else {
      return null;
    }
  };

  return (isLoading || !currentStep || !currentTask)
    ? (
        <WorkflowSkeleton />
      )
    : (
        <div className="grid grid-cols-12 border-t border-border h-[calc(100vh-64px)] overflow-hidden">
          {/* Right Sidebar with task list */}
          <div className={cn(
            'col-span-12 md:col-span-2 h-full overflow-y-auto border-r border-border',
          )}
          >
            <TaskList />
          </div>

          {/* Main content area */}
          <div className={cn(
            'col-span-12 md:col-span-10 h-full relative',
            isVisible ? 'lg:col-span-7' : 'lg:col-span-10',
          )}
          >
            <div className="absolute inset-0 overflow-y-auto">
              <div className="min-h-full flex flex-col">
                <div className="flex-grow relative">
                  {currentStep
                    ? (
                        <>
                          {currentStep.type === ETypeStep.RESEARCH || currentStep.type === ETypeStep.DOCUMENT_2
                            ? (
                                <></>
                              )
                            : (
                                <h6 className="p-4 md:p-6 pb-0!">
                                  {`${currentTask.order + 1}.${currentStep.order + 1}. ${currentStep.name[locale]}`}
                                </h6>
                              )}

                          {/* Render the appropriate step component */}
                          {renderStepComponent()}
                        </>
                      )
                    : (
                        <div className="p-4 md:p-6">Select a task to begin</div>
                      )}
                </div>
              </div>
            </div>
          </div>

          {/* Left sidebar with AI chat */}
          {isVisible && (
            <div className="hidden lg:block lg:col-span-3 h-full border-l border-border">
              <div className="h-full overflow-y-auto">
                <TaskSidebar>
                  <ChatBox />
                </TaskSidebar>
              </div>
            </div>
          )}
        </div>
      );
};

export default WorkflowWrapper;
