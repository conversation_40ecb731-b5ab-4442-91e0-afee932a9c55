export enum ENameStateAgentCopilotkit {
  SUMMARIZE = 'client_summarize_state',
  ASSESSMENT = 'client_assessment_state',
  ANALYSIS = 'brief_analysis_state',
  EDITING = 'content_editing_state',
  SCOPE = 'sow_analysis_state',
  QUOTATION = 'quotation_analysis_state',
};

export enum EEndpointApiCopilotkit {
  SUMMARIZE = 'client-summarize',
  ASSESSMENT = 'client-assessment',
  ANALYSIS = 'brief-analysis',
  EDITING = 'content_editing_state',
  SCOPE = 'sow-analysis',
  QUOTATION = 'quotation-analysis',
  ANSWER = 'answer-question-analysis',
  QUANTITY = 'generate-quantity-questionnaire',
  QUALITY = 'generate-quality-questionnaire',
  SUMMARY_QUANTITY = 'summary-quantity-questionnaire',
  SUMMARY_QUALITY = 'summary-quality-questionnaire',
  DESK_RESEARCH_ANSWER_QUESTION = 'desk-research-answer-question',
  DESK_RESEARCH_REPORT = 'desk-research-report',
  REPORT_QUANTITY = 'quantity-questionnaire-report',
  REPORT_QUALITY = 'quality-questionnaire-report',
  SUMMARY_REPORT = 'summary-report',
  TEMPLATE_GENERATE = 'template-generate',
  SCORING_CONTENT = 'scoring-content',
  EDIT_CLIENT_SUMMARIZE = 'edit-client-summarize',
  EDIT_CONTENT = 'edit-content',
  EDIT_QUANTITY_QUESTIONNAIRE = 'edit-quantity-questionnaire',
}

export enum ETaskNameCopilot {
  ANSWER_QUESTION_BRIEF = 'answer_question_brief',
  BRIEF_ANALYSIS = 'brief_analysis',
  SOW_ANALYSIS = 'sow_analysis',
  QUOTATION_ANALYSIS = 'quotation_analysis',
  RESEARCH_DESK_RESEARCH = 'answer_question_desk_research',
  SUMMARIZED_DESK_RESEARCH = 'desk_research_report',
  QUANTITY_QUESTIONNAIRE_REPORT = 'quantity_questionnaire_report',
  QUALITY_QUESTIONNAIRE = 'quality_questionnaire',
  QUALITY_ANALYSIS = 'quality_questionnaire_report',
  REPORT = 'report_summarize',
}

export enum EDescriptionCopilotkit {
  CLIENT_SUMMARIZE = 'Update table and value of client summarized. It is triggered whenever the user requests to modify/edit information within the table and value of client summarized',
  BRIEF_ANALYSIS = 'This function is used to update the Brief Analysis. It is triggered whenever the user requests to add, modify/edit, or delete information within the brief.',
  STANDARD_ANALYSIS = 'This function is used to update the Standard Analysis Brief. It is triggered whenever the user requests to add, modify/edit, or delete information within the brief.',
  SOW_ANALYSIS = 'This function is used to update the Scope of work (SOW). It is triggered whenever the user requests to add, modify/edit, or delete information within the Scope of work',
  QUOTATION_ANALYSIS = 'This function is used to update the quotation analysis. It is triggered whenever the user requests to add, modify/edit, or delete information within the quotation analysis',
  RESEARCH_DESK_RESEARCH = 'This function is used to update the desk research data. It is triggered whenever the user requests to add, modify/edit, or delete information within the desk research data',
  SUMMARIZED_DESK_RESEARCH = 'This function is used to update the desk research report. It is triggered whenever the user requests to add, modify/edit, or delete information within the desk research report',
  EDIT_QUANTITY_QUESTIONNAIRE = 'This function is used to update the enhance the questionnaire form. It is triggered whenever the user requests to add, modify/edit, or delete information within the questionnaire form',
  EDIT_QUANTITY_ANALYSIS = 'This function is used to update the enhance the quantity analysis data. It is triggered whenever the user requests to add, modify/edit, or delete information within the quantity analysis data',
  EDIT_QUALITY_QUESTIONNAIRE = 'This function is used to update the enhance the questionnaire quality. It is triggered whenever the user requests to add, modify/edit, or delete information within the questionnaire quality',
  EDIT_QUALITY_ANALYSIS = 'This function is used to update the enhance the analysis quality. It is triggered whenever the user requests to add, modify/edit, or delete information within the analysis quality',
  REPORT = 'This function is used to update the enhance final the report. It is triggered whenever the user requests to add, modify/edit, or delete information within the final report',
}
