import {
  CopilotRuntime,
  copilotRuntimeNextJSAppRouterEndpoint,
  OpenAIAdapter,
} from '@copilotkit/runtime';

import type { NextRequest } from 'next/server';

const serviceAdapter = new OpenAIAdapter({
  model: 'gpt-4o-mini',
},
);
const runtime = new CopilotRuntime();

export const POST = async (req: NextRequest) => {
  const { handleRequest } = copilotRuntimeNextJSAppRouterEndpoint({
    runtime,
    serviceAdapter,
    endpoint: '/api/ai-chat',
  });

  return handleRequest(req);
};
